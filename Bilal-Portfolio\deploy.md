# Vercel Deployment Guide

## Quick Deployment Steps

### 1. Prepare Your Repository
```bash
# Make sure all changes are committed
git add .
git commit -m "Prepare for Vercel deployment"
git push origin main
```

### 2. Deploy to Vercel

#### Option A: Using Vercel CLI (Recommended)
```bash
# Install Vercel CLI globally
npm install -g vercel

# Login to Vercel
vercel login

# Deploy from project root
vercel

# Follow the prompts:
# - Set up and deploy? Yes
# - Which scope? (select your account)
# - Link to existing project? No
# - Project name: bilal-portfolio (or your preferred name)
# - Directory: ./ (current directory)
# - Override settings? No
```

#### Option B: Using Vercel Dashboard
1. Go to https://vercel.com/dashboard
2. Click "New Project"
3. Import from GitHub: `umairumrani/Bilal-Portfolio`
4. Configure:
   - Framework Preset: Other
   - Build Command: `npm run build`
   - Output Directory: `dist/public`
   - Install Command: `npm install`

### 3. Configure Environment Variables

In your Vercel project dashboard:
1. Go to Settings → Environment Variables
2. Add the following variables:

```
DATABASE_URL = your_postgresql_connection_string
NODE_ENV = production
```

### 4. Database Setup Options

#### Option A: Vercel Postgres (Recommended)
1. In Vercel dashboard, go to Storage tab
2. Create a new Postgres database
3. Copy the connection string to DATABASE_URL

#### Option B: External Database (Neon, Railway, etc.)
1. Create a PostgreSQL database on your preferred provider
2. Copy the connection string to DATABASE_URL

### 5. Deploy
- If using CLI: `vercel --prod`
- If using dashboard: Push to main branch triggers auto-deployment

## Troubleshooting

### Build Errors
- Ensure all dependencies are in package.json
- Check that TypeScript compiles without errors: `npm run check`
- Verify build works locally: `npm run build`

### Runtime Errors
- Check Vercel function logs in dashboard
- Verify environment variables are set correctly
- Ensure database is accessible from Vercel

### Static Files Not Loading
- Verify build output is in `dist/public`
- Check vercel.json configuration
- Ensure static files are included in build

## Post-Deployment

1. **Test the deployment**
   - Visit your Vercel URL
   - Test contact form functionality
   - Check all pages load correctly

2. **Set up custom domain (optional)**
   - In Vercel dashboard, go to Settings → Domains
   - Add your custom domain

3. **Monitor performance**
   - Use Vercel Analytics
   - Check function execution times
   - Monitor database connections

## Useful Commands

```bash
# Local development
npm run dev

# Build for production
npm run build

# Type checking
npm run check

# Deploy to Vercel
vercel

# Deploy to production
vercel --prod

# View deployment logs
vercel logs
```
