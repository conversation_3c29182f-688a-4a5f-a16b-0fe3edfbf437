import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function AboutSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <section
      id='about'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        <motion.div
          className='max-w-5xl mx-auto'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <div className='space-y-16'>
            <div className='space-y-8'>
              <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight mb-6'>
                Meet the{" "}
                <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
                  Designer
                </span>
              </h2>

              <div className='space-y-4'>
                <p className='text-base md:text-lg text-gray-600 leading-relaxed'>
                  We are a creative agency that helps YouTubers and content
                  creators grow with high-CTR thumbnails, bold social media
                  designs, and expert visual strategy.
                </p>

                <p className='text-base md:text-lg text-gray-600 leading-relaxed'>
                  We work with vloggers, gamers, educators, and IRL creators to
                  craft scroll-stopping visuals that get clicks — fast.
                </p>

                <p className='text-base md:text-lg text-gray-600 leading-relaxed'>
                  Every design is driven by clarity, speed, and storytelling —
                  built to perform, not just look good.
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
