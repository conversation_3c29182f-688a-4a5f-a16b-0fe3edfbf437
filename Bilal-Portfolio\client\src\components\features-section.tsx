import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function FeaturesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const features = [
    {
      icon: "fas fa-lock",
      title: "End-to-End Solutions",
      description:
        "We are your one-stop shop for all your digital presence needs, providing solutions to help your business stand out online.",
      color: "from-blue-500 to-indigo-500",
    },
    {
      icon: "fas fa-user-friends",
      title: "Ongoing Maintenance & Support",
      description:
        "Updates, patches, fixes, and round-the-clock support to keep your systems running smoothly and efficiently.",
      color: "from-purple-500 to-pink-500",
    },
    {
      icon: "fas fa-credit-card",
      title: "Affordable Pricing",
      description:
        "Our services are reasonably priced and are of high quality, and customized to suit your requirements.",
      color: "from-green-500 to-teal-500",
    },
    {
      icon: "fas fa-clock",
      title: "Fast Turnaround Times",
      description:
        "Efficient management to ensure every project is delivered on time, meeting deadlines without compromising on quality.",
      color: "from-orange-500 to-red-500",
    },
  ];

  return (
    <section
      id='features'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Header */}
        <motion.div
          className='text-center space-y-6 mb-16'
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.p
            className='text-sm font-semibold text-[#FF4500] uppercase tracking-wide'
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            OUR FEATURES
          </motion.p>

          <motion.h2
            className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            We build ideas driven by{" "}
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
              the future.
            </span>
          </motion.h2>
        </motion.div>

        {/* Features Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12 max-w-6xl mx-auto'>
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className='group relative bg-white rounded-3xl p-8 md:p-10 shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 overflow-hidden'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.15 }}
              whileHover={{ y: -8, scale: 1.02 }}
            >
              {/* Background gradient on hover */}
              <div className='absolute inset-0 bg-gradient-to-br from-gray-50 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-500' />

              <div className='relative z-10'>
                {/* Icon */}
                <motion.div
                  className={`w-12 h-12 mb-6 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center shadow-lg`}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <i className={`${feature.icon} text-white text-xl`} />
                </motion.div>

                {/* Content */}
                <h3 className='text-xl md:text-2xl font-bold text-gray-900 mb-4 group-hover:text-[#FF4500] transition-colors duration-300'>
                  {feature.title}
                </h3>
                <p className='text-gray-600 leading-relaxed text-base md:text-lg'>
                  {feature.description}
                </p>
              </div>

              {/* Decorative elements */}
              <div className='absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500' />
              <div className='absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-[#FFA500]/5 to-[#FF4500]/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500' />

              {/* Subtle border effect */}
              <motion.div
                className={`absolute inset-0 border-2 border-transparent bg-gradient-to-r ${feature.color} rounded-3xl opacity-0 group-hover:opacity-10 transition-opacity duration-300`}
                style={{
                  mask: "linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)",
                  maskComposite: "exclude",
                }}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
