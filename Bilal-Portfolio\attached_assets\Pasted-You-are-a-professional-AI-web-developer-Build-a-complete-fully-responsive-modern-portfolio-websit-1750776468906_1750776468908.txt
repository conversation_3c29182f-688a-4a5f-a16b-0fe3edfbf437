You are a professional AI web developer. Build a complete, fully responsive, modern portfolio website for a **Thumbnail Designer** using HTML, CSS, and optionally JavaScript. The website should be designed with a **minimalist orange color palette** and have a **very clean, futuristic feel** — like something a premium content creator or designer would be proud to share.

💡 Here's the exact vision and requirements to follow:

🧠 GENERAL DESIGN STRATEGY:
- Use a **minimalist layout** with lots of white space.
- Accent color: **Bold, vibrant orange** (`#FFA500` or similar).
- Fonts: Clean, modern sans-serif (like **Poppins**, **Inter**, or **Outfit**).
- Make sure the design is **fully responsive** for mobile, tablet, and desktop.
- Smooth animations for hover effects and section transitions.
- Sticky navbar with scroll indicator.

📱 SITE STRUCTURE & CONTENT:

---

**1. Header (Hero Section)**  
- Large headline:  
  ➤ _“I Design Scroll-Stopping Thumbnails That Get Clicked.”_  
- Subheadline:  
  ➤ _“Hi, I’m <PERSON><PERSON> — A thumbnail artist who blends design psychology with viral strategy to help creators win more views.”_  
- CT<PERSON> Button: `View My Work`
- Background: clean, white or light gray with abstract orange elements.
- Thumbnail preview floating or animated (mockups).

---

**2. About Section**  
- Title: _"More Than Just Good Looks"_  
- Paragraph:  
  _“Designing thumbnails is about storytelling, contrast, and emotional triggers. With a sharp eye for detail and YouTube’s algorithm in mind, I turn ordinary ideas into irresistible visuals. I’ve worked with creators in tech, fitness, vlogs, and more — now I want to help you grow too.”_  
- Small image of you or a design mockup with orange border.
- Optional: “My Tools” with icons (Photoshop, Figma, Canva, etc.)

---

**3. Portfolio Section**  
- Grid layout of **6 to 8 thumbnails** in cards.
- Each thumbnail should have:
  - Hover effect (zoom or fade)
  - Optional tags like `Tech`, `Fitness`, `Entertainment`
- Title: _"Recent Designs"_  
- Button: `Download My Portfolio PDF` or `Let’s Work Together`

---

**4. Testimonials Section (optional)**  
- Carousel or grid with client feedback.
- Heading: _"What Clients Say"_  
- Add 2-3 fake testimonials with names and roles (e.g. “Ali – Tech YouTuber”).

---

**5. Contact Section**  
- Heading: _"Let’s Make Your Channel Unstoppable."_  
- Short form with:
  - Name, Email, Message
  - Subject dropdown (Thumbnail, Consultation, Collaboration)
  - Submit Button (styled orange, hover effect)
- Add your Gmail or WhatsApp for direct contact.

---

**6. Footer**  
- Left: "© 2025 Umair | Thumbnail Designer"
- Right: Social Icons: Instagram, LinkedIn, YouTube (orange hover color)

---

🎨 COLOR PALETTE  
- Background: #FFFFFF (white), #F5F5F5 (light gray)  
- Accent: #FF6F00, #FFA500 (various oranges)  
- Text: #1C1C1C or #333333 (dark gray)

---

🛠 TECHNICAL REQUIREMENTS:
- Use clean, semantic HTML5.
- CSS should be modular or written in a maintainable way (consider BEM or simple utility classes).
- Add media queries for responsiveness.
- You can use minimal JS for animations (e.g. scroll reveal, form validation).
- Animate on scroll using Intersection Observer or a lightweight library if needed.

---

📌 BONUS IDEAS (if time permits):
- Add a **dark mode toggle**
- Add a **"Before vs After" slider** for thumbnails.
- Add a **YouTube thumbnail strategy guide** as a downloadable lead magnet.
- Add a **“Stats” section**: Total thumbnails made, Avg. CTR boost, Total views gained, etc.

---

📦 FINAL OUTPUT:
- Deliver full working code: `index.html`, `style.css`, and optional `script.js`.
- Keep code clean, commented, and well-structured.

