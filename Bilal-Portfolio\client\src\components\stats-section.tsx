import { motion, useSpring, useTransform } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function StatsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [counters, setCounters] = useState({
    thumbnails: 0,
    experience: 0,
    satisfaction: 0,
  });

  // Spring animations for smooth counting
  const thumbnailsSpring = useSpring(0, { stiffness: 100, damping: 30 });
  const experienceSpring = useSpring(0, { stiffness: 100, damping: 30 });
  const satisfactionSpring = useSpring(0, { stiffness: 100, damping: 30 });

  const stats = [
    {
      number: 250,
      suffix: "+",
      title: "Thumbnails Designed",
      description:
        "Over 250 custom thumbnails delivered for creators in gaming, vlogging, education, and IRL categories. Each design crafted to boost clicks and channel growth.",
      icon: "fas fa-image",
      color: "from-orange-500 to-red-500",
    },
    {
      number: 6,
      suffix: "+",
      title: "Years of Creative Experience",
      description:
        "More than six years of hands-on design experience, providing reliable and high-performance visuals tailored for YouTube and social media platforms.",
      icon: "fas fa-calendar-alt",
      color: "from-blue-500 to-indigo-500",
    },
    {
      number: 98,
      suffix: "%",
      title: "Client Satisfaction Rate",
      description:
        "Maintaining a 98% satisfaction score by consistently delivering fast, effective, and results-driven designs that meet creator needs.",
      icon: "fas fa-heart",
      color: "from-green-500 to-teal-500",
    },
  ];

  // Animated counter effect
  useEffect(() => {
    if (isInView) {
      const duration = 2000; // 2 seconds
      const steps = 60;
      const stepTime = duration / steps;

      stats.forEach((stat, index) => {
        let currentStep = 0;
        const increment = stat.number / steps;

        const timer = setInterval(() => {
          currentStep++;
          const currentValue = Math.min(
            Math.floor(increment * currentStep),
            stat.number
          );

          setCounters((prev) => ({
            ...prev,
            [index === 0
              ? "thumbnails"
              : index === 1
              ? "experience"
              : "satisfaction"]: currentValue,
          }));

          if (currentStep >= steps) {
            clearInterval(timer);
          }
        }, stepTime);
      });
    }
  }, [isInView]);

  return (
    <section
      id='stats'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Stats Grid */}
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12'>
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              className='group relative bg-gradient-to-br from-gray-50 to-white rounded-3xl p-8 md:p-10 shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 overflow-hidden text-center'
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{ y: -8, scale: 1.02 }}
            >
              {/* Background gradient on hover */}
              <div className='absolute inset-0 bg-gradient-to-br from-white to-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500' />

              <div className='relative z-10 space-y-6'>
                {/* Icon */}
                <motion.div
                  className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center shadow-lg`}
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                >
                  <i className={`${stat.icon} text-white text-2xl`} />
                </motion.div>

                {/* Number with Creative Animation */}
                <div className='space-y-2 relative'>
                  {/* Animated Background Circle */}
                  <motion.div
                    className='absolute inset-0 rounded-full bg-gradient-to-r from-[#FF4500]/10 to-[#FF6B35]/10'
                    initial={{ scale: 0, rotate: 0 }}
                    animate={
                      isInView
                        ? { scale: 1.2, rotate: 360 }
                        : { scale: 0, rotate: 0 }
                    }
                    transition={{
                      duration: 2,
                      delay: index * 0.3,
                      type: "spring",
                      stiffness: 100,
                    }}
                  />

                  <motion.h3
                    className='text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent relative z-10'
                    initial={{ scale: 0, rotateY: -180 }}
                    animate={
                      isInView
                        ? { scale: 1, rotateY: 0 }
                        : { scale: 0, rotateY: -180 }
                    }
                    transition={{
                      duration: 1.2,
                      delay: index * 0.2 + 0.5,
                      type: "spring",
                      stiffness: 200,
                      damping: 20,
                    }}
                    whileHover={{
                      scale: 1.1,
                      textShadow: "0 0 20px rgba(255, 69, 0, 0.5)",
                    }}
                  >
                    {index === 0
                      ? counters.thumbnails
                      : index === 1
                      ? counters.experience
                      : counters.satisfaction}
                    {stat.suffix}
                  </motion.h3>

                  <motion.h4
                    className='text-xl md:text-2xl font-bold text-gray-900 group-hover:text-[#FF4500] transition-colors duration-300 relative z-10'
                    initial={{ opacity: 0, y: 20 }}
                    animate={
                      isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                    }
                    transition={{ duration: 0.8, delay: index * 0.2 + 0.8 }}
                  >
                    {stat.title}
                  </motion.h4>
                </div>

                {/* Description */}
                <p className='text-gray-600 leading-relaxed text-base'>
                  {stat.description}
                </p>
              </div>

              {/* Decorative elements */}
              <div className='absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500' />
              <div className='absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-[#FFA500]/5 to-[#FF4500]/5 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500' />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
