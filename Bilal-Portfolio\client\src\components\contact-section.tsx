import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

// Contact form schema for frontend validation
const contactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  subject: z.string().min(1, "Please select a subject"),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ContactForm = z.infer<typeof contactSchema>;

export default function ContactSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContactForm>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      message: "",
    },
  });

  const onSubmit = async (data: ContactForm) => {
    setIsSubmitting(true);

    // Simulate form submission delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Create mailto link with form data
    const subject = encodeURIComponent(
      `${data.subject} - Contact from ${data.name}`
    );
    const body = encodeURIComponent(
      `Name: ${data.name}\nEmail: ${data.email}\nSubject: ${data.subject}\n\nMessage:\n${data.message}`
    );
    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

    // Open email client
    window.open(mailtoLink, "_blank");

    // Show success message
    toast({
      title: "Ready to send!",
      description:
        "Your email client should open with the message pre-filled. You can also contact me <NAME_EMAIL>",
    });

    form.reset();
    setIsSubmitting(false);
  };

  return (
    <section
      id='contact'
      className='pt-8 pb-16 md:pt-12 md:pb-24 lg:pt-16 lg:pb-32 px-6 lg:px-12 bg-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        <div className='grid lg:grid-cols-2 gap-20 items-center'>
          {/* Content */}
          <motion.div
            className='space-y-12'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <div className='space-y-8'>
              <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight'>
                Let's Make Your Channel{" "}
                <span className='text-gradient'>Unstoppable.</span>
              </h2>

              <p className='text-lg md:text-xl text-gray-600 leading-relaxed'>
                Ready to see your click-through rates soar? Let's create
                thumbnails that turn browsers into viewers and viewers into
                subscribers.
              </p>
            </div>

            {/* Contact Info */}
            <div className='space-y-6'>
              <div className='flex items-center space-x-4'>
                <div className='w-12 h-12 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center text-white'>
                  <i className='fas fa-envelope'></i>
                </div>
                <div>
                  <h4 className='font-semibold text-gray-900'>Email</h4>
                  <p className='text-gray-600'><EMAIL></p>
                </div>
              </div>

              <div className='flex items-center space-x-4'>
                <div className='w-12 h-12 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center text-white'>
                  <i className='fab fa-whatsapp'></i>
                </div>
                <div>
                  <h4 className='font-semibold text-gray-900'>WhatsApp</h4>
                  <p className='text-gray-600'>+92 334 0505686</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            className='relative bg-gradient-to-br from-white/90 via-white/95 to-white/90 backdrop-blur-xl rounded-3xl p-6 md:p-8 shadow-2xl border border-white/20 max-w-2xl mx-auto'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
          >
            {/* Form Header */}
            <div className='text-center mb-6'>
              <motion.h3
                className='text-xl md:text-2xl font-bold text-gray-900 mb-2'
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Start Your Project
              </motion.h3>
              <motion.p
                className='text-gray-600 text-base'
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                Tell me about your vision and let's create something amazing
                together
              </motion.p>
            </div>

            {/* Decorative Elements */}
            <div className='absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-[#FF4500]/10 to-[#FF6B35]/10 rounded-full blur-xl' />
            <div className='absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-[#FFA500]/10 to-[#FF4500]/10 rounded-full blur-xl' />
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-5 relative z-10'
              >
                <div className='grid md:grid-cols-2 gap-6'>
                  <FormField
                    control={form.control}
                    name='name'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='text-gray-700 font-medium flex items-center gap-2'>
                          <i className='fas fa-user text-[#FF4500]' />
                          Name
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder='Your Name'
                            className='px-4 py-3 h-12 border-2 border-gray-200/60 rounded-xl focus:ring-4 focus:ring-[#FF4500]/20 focus:border-[#FF4500] transition-all duration-300 bg-white/70 backdrop-blur-sm hover:border-[#FF4500]/60 hover:shadow-xl hover:bg-white/90 text-gray-800 placeholder:text-gray-500'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='email'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='text-gray-700 font-medium flex items-center gap-2'>
                          <i className='fas fa-envelope text-[#FF4500]' />
                          Email
                        </FormLabel>
                        <FormControl>
                          <Input
                            type='email'
                            placeholder='<EMAIL>'
                            className='px-4 py-3 h-12 border-2 border-gray-200/60 rounded-xl focus:ring-4 focus:ring-[#FF4500]/20 focus:border-[#FF4500] transition-all duration-300 bg-white/70 backdrop-blur-sm hover:border-[#FF4500]/60 hover:shadow-xl hover:bg-white/90 text-gray-800 placeholder:text-gray-500'
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name='subject'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-gray-700 font-medium flex items-center gap-2'>
                        <i className='fas fa-list-ul text-[#FF4500]' />
                        Service Type
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className='group px-4 py-3 h-12 border-2 border-gray-200/60 rounded-xl focus:ring-4 focus:ring-[#FF4500]/20 focus:border-[#FF4500] transition-all duration-300 bg-white/70 backdrop-blur-sm hover:border-[#FF4500]/60 hover:shadow-xl hover:bg-white/90'>
                            <SelectValue
                              placeholder='Select a service type'
                              className='text-gray-600 group-hover:text-gray-800 transition-colors duration-300'
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className='bg-white/95 backdrop-blur-xl border-2 border-gray-200/60 rounded-xl shadow-2xl p-2'>
                          <SelectItem
                            value='youtube-thumbnail'
                            className='px-4 py-4 hover:bg-gradient-to-r hover:from-[#FF4500]/15 hover:to-[#FF6B35]/15 rounded-xl transition-all duration-300 cursor-pointer text-gray-700 hover:text-gray-900 font-medium'
                          >
                            <div className='flex items-center gap-3'>
                              <i className='fab fa-youtube text-[#FF4500]' />
                              Youtube Thumbnail Design
                            </div>
                          </SelectItem>
                          <SelectItem
                            value='social-media'
                            className='px-4 py-4 hover:bg-gradient-to-r hover:from-[#FF4500]/15 hover:to-[#FF6B35]/15 rounded-xl transition-all duration-300 cursor-pointer text-gray-700 hover:text-gray-900 font-medium'
                          >
                            <div className='flex items-center gap-3'>
                              <i className='fas fa-share-alt text-[#FF4500]' />
                              Social Media Design
                            </div>
                          </SelectItem>
                          <SelectItem
                            value='profile-branding'
                            className='px-4 py-4 hover:bg-gradient-to-r hover:from-[#FF4500]/15 hover:to-[#FF6B35]/15 rounded-xl transition-all duration-300 cursor-pointer text-gray-700 hover:text-gray-900 font-medium'
                          >
                            <div className='flex items-center gap-3'>
                              <i className='fas fa-palette text-[#FF4500]' />
                              Profile Branding Design
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='message'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-gray-700 font-medium flex items-center gap-2'>
                        <i className='fas fa-comment-dots text-[#FF4500]' />
                        Message
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Tell me about your project, goals, timeline, and any specific requirements...'
                          rows={4}
                          className='px-4 py-3 border-2 border-gray-200/60 rounded-xl focus:ring-4 focus:ring-[#FF4500]/20 focus:border-[#FF4500] transition-all duration-300 bg-white/70 backdrop-blur-sm resize-none hover:border-[#FF4500]/60 hover:shadow-xl hover:bg-white/90 text-gray-800 placeholder:text-gray-500'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{ duration: 0.2 }}
                >
                  <Button
                    type='submit'
                    disabled={isSubmitting}
                    className='w-full h-12 bg-gradient-to-r from-[#FF4500] via-[#FF6B35] to-[#FFA500] text-white font-bold text-base rounded-xl hover:shadow-2xl hover:from-[#FF6B35] hover:via-[#FF4500] hover:to-[#FF6B35] transition-all duration-500 flex items-center justify-center gap-3 disabled:opacity-70 disabled:cursor-not-allowed relative overflow-hidden group'
                  >
                    {/* Button Background Animation */}
                    <div className='absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000' />

                    <span className='relative z-10'>
                      {isSubmitting
                        ? "Preparing Your Message..."
                        : "Send Message"}
                    </span>
                    <motion.i
                      className={`fas ${
                        isSubmitting ? "fa-spinner fa-spin" : "fa-paper-plane"
                      } relative z-10`}
                      animate={isSubmitting ? {} : { x: [0, 3, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </Button>
                </motion.div>
              </form>
            </Form>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
