import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState, useEffect } from "react";

export default function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [satisfactionRate, setSatisfactionRate] = useState(0);

  const testimonials = [
    {
      text: "I have been using Zebnex for some time now and all I can say is that their service is top notch! Quick service and the quality is always exceptional. I was so pleased with the results!",
      name: "HATTIE NIELSEN",
      role: "Content Creator",
      avatar: "/testimonial-avatar-1.jpg",
      rating: 5,
    },
    {
      text: "<PERSON><PERSON><PERSON> isn't just fast — he understands what makes people click. My views shot up 40% after switching to his thumbnails!",
      name: "<PERSON>",
      role: "Tech YouTuber • 250K Subscribers",
      avatar: "/testimonial-avatar-2.jpg",
      rating: 5,
    },
    {
      text: "Professional, creative, and results-driven. <PERSON><PERSON><PERSON> doesn't just create beautiful thumbnails – he creates thumbnails that convert viewers into subscribers.",
      name: "<PERSON> Chen",
      role: "Fitness Influencer • 180K Subscribers",
      avatar: "/testimonial-avatar-3.jpg",
      rating: 5,
    },
  ];

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  // Animate satisfaction rate
  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        let current = 0;
        const target = 98.6;
        const increment = target / 60;

        const counter = setInterval(() => {
          current += increment;
          if (current >= target) {
            setSatisfactionRate(target);
            clearInterval(counter);
          } else {
            setSatisfactionRate(current);
          }
        }, 30);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isInView]);

  return (
    <section
      id='testimonials'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        <div className='grid lg:grid-cols-2 gap-16 items-center'>
          {/* Left Side - Satisfaction Rate */}
          <motion.div
            className='space-y-8'
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            {/* Satisfaction Rate Circle */}
            <div className='relative w-64 h-64 mx-auto lg:mx-0'>
              <svg
                className='w-full h-full transform -rotate-90'
                viewBox='0 0 100 100'
              >
                {/* Background circle */}
                <circle
                  cx='50'
                  cy='50'
                  r='45'
                  fill='none'
                  stroke='#e5e7eb'
                  strokeWidth='8'
                />
                {/* Progress circle */}
                <motion.circle
                  cx='50'
                  cy='50'
                  r='45'
                  fill='none'
                  stroke='url(#gradient)'
                  strokeWidth='8'
                  strokeLinecap='round'
                  strokeDasharray={`${2 * Math.PI * 45}`}
                  strokeDashoffset={`${
                    2 * Math.PI * 45 * (1 - satisfactionRate / 100)
                  }`}
                  initial={{ strokeDashoffset: 2 * Math.PI * 45 }}
                  animate={{
                    strokeDashoffset:
                      2 * Math.PI * 45 * (1 - satisfactionRate / 100),
                  }}
                  transition={{ duration: 2, ease: "easeOut", delay: 0.5 }}
                />
                <defs>
                  <linearGradient
                    id='gradient'
                    x1='0%'
                    y1='0%'
                    x2='100%'
                    y2='100%'
                  >
                    <stop offset='0%' stopColor='#FF4500' />
                    <stop offset='100%' stopColor='#FF6B35' />
                  </linearGradient>
                </defs>
              </svg>

              {/* Center content */}
              <div className='absolute inset-0 flex flex-col items-center justify-center'>
                <motion.span
                  className='text-4xl md:text-5xl font-bold bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'
                  initial={{ scale: 0 }}
                  animate={isInView ? { scale: 1 } : { scale: 0 }}
                  transition={{
                    duration: 0.8,
                    delay: 1,
                    type: "spring",
                    stiffness: 200,
                  }}
                >
                  {satisfactionRate.toFixed(1)}%
                </motion.span>
                <span className='text-sm font-medium text-gray-600 mt-1'>
                  Customer Satisfaction
                </span>
              </div>
            </div>

            {/* Header Text */}
            <div className='text-center lg:text-left space-y-4'>
              <h2 className='text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight'>
                Testimonials from{" "}
                <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] bg-clip-text text-transparent'>
                  our customers.
                </span>
              </h2>
              <p className='text-gray-600 leading-relaxed'>
                Read our clients' success stories and discover how our services
                helped elevate their brands.
              </p>
            </div>
          </motion.div>

          {/* Right Side - Testimonial Carousel */}
          <motion.div
            className='relative'
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: 50 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
          >
            {/* Main Testimonial Card */}
            <div className='relative bg-white rounded-3xl p-8 md:p-10 shadow-2xl border border-gray-100 overflow-hidden'>
              {/* Background decoration */}
              <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-[#FF4500]/5 to-[#FF6B35]/5 rounded-full blur-3xl' />

              <div className='relative z-10 space-y-6'>
                {/* Stars */}
                <div className='flex text-[#FF4500] text-xl'>
                  {[...Array(testimonials[currentTestimonial].rating)].map(
                    (_, i) => (
                      <motion.i
                        key={i}
                        className='fas fa-star'
                        initial={{ opacity: 0, scale: 0 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.3, delay: i * 0.1 }}
                      />
                    )
                  )}
                </div>

                {/* Testimonial Text */}
                <motion.p
                  key={currentTestimonial}
                  className='text-gray-700 leading-relaxed text-lg'
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  "{testimonials[currentTestimonial].text}"
                </motion.p>

                {/* Author */}
                <motion.div
                  key={`author-${currentTestimonial}`}
                  className='flex items-center space-x-4'
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <div className='w-14 h-14 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center overflow-hidden'>
                    {testimonials[currentTestimonial].avatar.startsWith("/") ? (
                      <img
                        src={testimonials[currentTestimonial].avatar}
                        alt={testimonials[currentTestimonial].name}
                        className='w-full h-full object-cover'
                      />
                    ) : (
                      <span className='text-white font-bold text-lg'>
                        {testimonials[currentTestimonial].avatar}
                      </span>
                    )}
                  </div>
                  <div>
                    <h4 className='font-bold text-gray-900 text-lg'>
                      {testimonials[currentTestimonial].name}
                    </h4>
                    <p className='text-gray-600'>
                      {testimonials[currentTestimonial].role}
                    </p>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* Navigation Dots */}
            <div className='flex justify-center space-x-3 mt-8'>
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentTestimonial
                      ? "bg-gradient-to-r from-[#FF4500] to-[#FF6B35] w-8"
                      : "bg-gray-300 hover:bg-gray-400"
                  }`}
                />
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
