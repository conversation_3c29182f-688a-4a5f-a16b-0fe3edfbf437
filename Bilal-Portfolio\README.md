# Bilal Portfolio

A modern, full-stack portfolio website for BilalThumb<PERSON><PERSON>, a professional thumbnail designer.

## Tech Stack

- **Frontend**: React 18 + TypeScript, Tailwind CSS, Vite
- **Backend**: Express.js + TypeScript
- **Database**: PostgreSQL with Dr<PERSON>zle ORM
- **UI Components**: Radix UI with shadcn/ui
- **Animations**: Framer Motion
- **Deployment**: Vercel

## Features

- Responsive design
- Modern UI with smooth animations
- Contact form with backend API
- Portfolio showcase
- Professional thumbnail design gallery

## Local Development

1. **Clone the repository**

   ```bash
   git clone https://github.com/umairumrani/Bilal-Portfolio.git
   cd Bilal-Portfolio
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your database credentials.

4. **Run the development server**
   ```bash
   npm run dev
   ```
   The application will be available at `http://localhost:5000`

## Deployment on Vercel

### Prerequisites

- Vercel account
- PostgreSQL database (Vercel Postgres, Neon, or any other provider)

### Steps

1. **Fork/Clone this repository to your GitHub account**

2. **Connect to Vercel**

   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository

3. **Configure Environment Variables**
   In your Vercel project settings, add:

   ```
   DATABASE_URL=your_postgresql_connection_string
   NODE_ENV=production
   ```

4. **Deploy**
   - Vercel will automatically build and deploy your application
   - The build command is: `npm run build`
   - The application will be served from the `api/index.ts` serverless function

### Build Process

The project uses a custom build process optimized for Vercel:

- Frontend is built using Vite and outputs to `dist/public`
- Backend runs as a Vercel serverless function
- Static files are served from the built frontend

## Project Structure

```
Bilal-Portfolio/
├── api/                    # Vercel serverless functions
│   └── index.ts           # Main API entry point
├── client/                # Frontend React application
│   ├── src/
│   └── index.html
├── server/                # Backend Express application
│   ├── index.ts          # Development server
│   ├── routes.ts         # API routes
│   ├── storage.ts        # Data storage layer
│   └── vite.ts           # Vite configuration
├── shared/               # Shared types and schemas
├── dist/                 # Build output (generated)
├── vercel.json          # Vercel deployment configuration
└── package.json
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server (local)
- `npm run check` - Type checking
- `npm run db:push` - Push database schema changes

## Environment Variables

| Variable       | Description                          | Required |
| -------------- | ------------------------------------ | -------- |
| `DATABASE_URL` | PostgreSQL connection string         | Yes      |
| `NODE_ENV`     | Environment (development/production) | Yes      |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
