import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";

export default function BeforeAfterSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  // Before/after example using uploaded thumbnails
  const beforeAfterExample = {
    id: 1,
    title: "YouTube Thumbnail Transformation",
    description:
      "See how professional design can dramatically improve your thumbnail's impact",
    before: "/Before.png",
    after: "/After.png",
    category: "YouTube",
    improvement: "Professional Design",
  };

  const BeforeAfterSlider = ({ example }: { example: any }) => {
    const [sliderPosition, setSliderPosition] = useState(50);
    const [isDragging, setIsDragging] = useState(false);

    const handleMouseDown = () => setIsDragging(true);
    const handleMouseUp = () => setIsDragging(false);

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
      if (!isDragging) return;

      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const percentage = (x / rect.width) * 100;
      setSliderPosition(Math.max(0, Math.min(100, percentage)));
    };

    const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
      if (!isDragging) return;

      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.touches[0].clientX - rect.left;
      const percentage = (x / rect.width) * 100;
      setSliderPosition(Math.max(0, Math.min(100, percentage)));
    };

    return (
      <div className='relative w-full max-w-4xl mx-auto'>
        {/* Main Slider Container */}
        <div
          className='relative w-full h-96 md:h-[500px] rounded-3xl overflow-hidden cursor-col-resize select-none shadow-2xl border-4 border-white'
          onMouseMove={handleMouseMove}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onTouchStart={handleMouseDown}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleMouseUp}
        >
          {/* Before Image */}
          <div className='absolute inset-0'>
            <img
              src={example.before}
              alt={`${example.title} - Before`}
              className='w-full h-full object-cover'
              draggable={false}
            />
            <div className='absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold'>
              BEFORE
            </div>
          </div>

          {/* After Image */}
          <div
            className='absolute inset-0 overflow-hidden'
            style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
          >
            <img
              src={example.after}
              alt={`${example.title} - After`}
              className='w-full h-full object-cover'
              draggable={false}
            />
            <div className='absolute top-4 right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold'>
              AFTER
            </div>
          </div>

          {/* Slider Handle */}
          <div
            className='absolute top-0 bottom-0 w-1 bg-white shadow-2xl cursor-col-resize z-10'
            style={{ left: `${sliderPosition}%` }}
          >
            <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-2xl flex items-center justify-center border-4 border-gray-100 hover:scale-110 transition-transform duration-200'>
              <div className='flex space-x-1'>
                <div className='w-1 h-4 bg-gradient-to-b from-[#FF4500] to-[#FF6B35] rounded-full'></div>
                <div className='w-1 h-4 bg-gradient-to-b from-[#FF4500] to-[#FF6B35] rounded-full'></div>
              </div>
            </div>
          </div>

          {/* Instruction Text */}
          <div className='absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/70 text-white px-4 py-2 rounded-full text-sm font-medium backdrop-blur-sm'>
            <i className='fas fa-arrows-alt-h mr-2'></i>
            Drag to compare
          </div>
        </div>

        {/* Example Info */}
        <motion.div
          className='mt-8 text-center'
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className='inline-flex items-center space-x-2 mb-4'>
            <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white px-3 py-1 rounded-full text-sm font-semibold'>
              {example.category}
            </span>
            <span className='bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold'>
              {example.improvement}
            </span>
          </div>
          <h3 className='text-2xl md:text-3xl font-bold text-gray-900 mb-2'>
            {example.title}
          </h3>
          <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
            {example.description}
          </p>
        </motion.div>
      </div>
    );
  };

  return (
    <section
      id='before-after'
      className='py-16 md:py-24 lg:py-32 px-6 lg:px-12 bg-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Section Header */}
        <motion.div
          className='text-center mb-16'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6'>
            See The <span className='text-gradient'>Transformation</span>
          </h2>
          <p className='text-lg md:text-xl text-gray-600 max-w-3xl mx-auto'>
            Real results from real creators. Drag the slider to see how
            professional thumbnail design can dramatically improve your
            channel's performance.
          </p>
        </motion.div>

        {/* Before/After Slider */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <BeforeAfterSlider example={beforeAfterExample} />
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className='text-center mt-16'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <motion.a
            href='#contact'
            className='inline-block bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white font-semibold text-lg px-8 py-4 rounded-2xl hover:shadow-xl transition-all duration-300'
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            Get Your Transformation Started
          </motion.a>
        </motion.div>
      </div>
    </section>
  );
}
