import { useEffect } from "react";
import Navigation from "@/components/navigation";
import HeroSection from "@/components/hero-section";
import AboutSection from "@/components/about-section";
import OffersSection from "@/components/offers-section";
import BeforeAfterSection from "@/components/before-after-section";
import ServicesSection from "@/components/services-section";
import PortfolioSection from "@/components/portfolio-section";
import FeaturesSection from "@/components/features-section";
import TestimonialsSection from "@/components/testimonials-section";
import StatsSection from "@/components/stats-section";
import ProblemSolutionSection from "@/components/problem-solution-section";
import ContactSection from "@/components/contact-section";
import Footer from "@/components/footer";

export default function Home() {
  useEffect(() => {
    // Smooth scrolling for navigation links
    const handleLinkClick = (e: Event) => {
      const target = e.target as HTMLAnchorElement;
      if (target.getAttribute("href")?.startsWith("#")) {
        e.preventDefault();
        const element = document.querySelector(target.getAttribute("href")!);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }
    };

    document.addEventListener("click", handleLinkClick);
    return () => document.removeEventListener("click", handleLinkClick);
  }, []);

  return (
    <div className='min-h-screen bg-white text-gray-900'>
      <Navigation />
      <HeroSection />
      <ServicesSection />
      <PortfolioSection />
      <FeaturesSection />
      <BeforeAfterSection />
      <TestimonialsSection />
      <AboutSection />
      <StatsSection />
      <ProblemSolutionSection />
      <OffersSection />
      <ContactSection />
      <Footer />
    </div>
  );
}
