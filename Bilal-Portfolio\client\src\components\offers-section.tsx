import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef, useState } from "react";

export default function OffersSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [selectedCategory, setSelectedCategory] = useState("thumbnails");

  const categories = [
    {
      id: "thumbnails",
      name: "YouTube Thumbnail Design",
      icon: "fas fa-image",
    },
    {
      id: "banners",
      name: "YouTube Banner & Profile Design",
      icon: "fas fa-paint-brush",
    },
    {
      id: "social",
      name: "Social Media Post Design",
      icon: "fas fa-share-alt",
    },
  ];

  const thumbnailPlans = [
    {
      name: "Starter",
      price: "$50",
      period: "/month",
      features: [
        "5 thumbnails/month",
        "Unlimited revisions",
        "Fast 1-2 day delivery",
        "Up to 3 requests at once",
        "Cancel anytime",
      ],
      color: "from-orange-500 to-red-500",
      popular: false,
    },
    {
      name: "Standard",
      price: "$100",
      period: "/month",
      features: [
        "10 thumbnails/month",
        "Unlimited revisions",
        "Fast 1-2 day delivery",
        "Up to 3 requests at once",
        "Cancel anytime",
      ],
      color: "from-blue-500 to-indigo-500",
      popular: true,
    },
    {
      name: "Surge",
      price: "$150",
      period: "/month",
      features: [
        "15 thumbnails/month",
        "Unlimited revisions",
        "Fast 1-2 day delivery",
        "Up to 3 requests at once",
        "Cancel anytime",
      ],
      color: "from-purple-500 to-pink-500",
      popular: false,
    },
  ];

  const bannerPlans = [
    {
      name: "Profile Only",
      price: "$5",
      period: "(One-Time)",
      features: [
        "Custom YouTube Profile Picture",
        "High-Quality & Unique Design",
        "Optimized for YouTube Display",
        "Unlimited Revisions",
        "Fast 1-2 Day Delivery",
      ],
      color: "from-green-500 to-teal-500",
    },
    {
      name: "Banner Only",
      price: "$15",
      period: "(One-Time)",
      features: [
        "Custom YouTube Banner",
        "Professional & Eye-Catching Design",
        "Optimized for All Devices",
        "Unlimited Revisions",
        "Fast 1-2 Day Delivery",
      ],
      color: "from-yellow-500 to-orange-500",
    },
    {
      name: "Complete Branding",
      price: "$20",
      period: "(One-Time)",
      features: [
        "Custom YouTube Profile Picture",
        "Custom YouTube Banner",
        "Consistent Branding Across Both",
        "Optimized for All Devices",
        "Unlimited Revisions",
        "Fast 1-2 Day Delivery",
      ],
      color: "from-indigo-500 to-purple-500",
      popular: true,
    },
  ];

  const socialMediaPlans = [
    {
      name: "Starter",
      price: "$50",
      period: "/month",
      features: [
        "5 Social Media Posts",
        "Custom, High-Quality Designs",
        "Optimized for Instagram, Facebook, Twitter & LinkedIn",
        "Unlimited Revisions",
        "Fast 1-2 Day Delivery",
        "Cancel Anytime",
      ],
      color: "from-pink-500 to-rose-500",
    },
    {
      name: "Standard",
      price: "$100",
      period: "/month",
      features: [
        "10 Social Media Posts",
        "Custom, High-Quality Designs",
        "Optimized for Instagram, Facebook, Twitter & LinkedIn",
        "Unlimited Revisions",
        "Fast 1-2 Day Delivery",
        "Cancel Anytime",
      ],
      color: "from-cyan-500 to-blue-500",
      popular: true,
    },
    {
      name: "Surge",
      price: "$150",
      period: "/month",
      features: [
        "15 Social Media Posts",
        "Custom, High-Quality Designs",
        "Optimized for Instagram, Facebook, Twitter & LinkedIn",
        "Unlimited Revisions",
        "Fast 1-2 Day Delivery",
        "Cancel Anytime",
      ],
      color: "from-emerald-500 to-green-500",
    },
  ];

  const getCurrentPlans = () => {
    switch (selectedCategory) {
      case "thumbnails":
        return thumbnailPlans;
      case "banners":
        return bannerPlans;
      case "social":
        return socialMediaPlans;
      default:
        return thumbnailPlans;
    }
  };

  const PricingCard = ({ plan, index, delay = 0 }: any) => (
    <motion.div
      className={`relative rounded-3xl p-6 sm:p-8 h-full flex flex-col shadow-lg border border-gray-100 ${
        plan.popular
          ? "bg-gradient-to-br from-orange-50 to-red-50 ring-2 ring-[#FF4500] scale-105 shadow-xl"
          : "bg-gradient-to-br from-white to-gray-50 hover:shadow-xl"
      }`}
      initial={{ opacity: 0, y: 30 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
      transition={{ duration: 0.5, delay: delay + index * 0.1 }}
      whileHover={{ scale: plan.popular ? 1.05 : 1.02, y: -5 }}
    >
      {plan.popular && (
        <div className='absolute -top-4 left-1/2 transform -translate-x-1/2'>
          <span className='bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white px-4 py-2 rounded-full text-sm font-semibold'>
            Most Popular
          </span>
        </div>
      )}

      <div className='text-center mb-8'>
        <h3 className='text-2xl font-bold text-gray-900 mb-2'>{plan.name}</h3>
        <div className='flex items-baseline justify-center'>
          <span className='text-4xl font-bold text-gray-900'>{plan.price}</span>
          <span className='text-gray-600 ml-1'>{plan.period}</span>
        </div>
      </div>

      <ul className='space-y-4 mb-8 flex-grow'>
        {plan.features.map((feature: string, idx: number) => (
          <li key={idx} className='flex items-center space-x-3'>
            <div className='w-5 h-5 bg-gradient-to-r from-[#FF4500] to-[#FF6B35] rounded-full flex items-center justify-center flex-shrink-0'>
              <i className='fas fa-check text-white text-xs'></i>
            </div>
            <span className='text-gray-600 text-sm'>{feature}</span>
          </li>
        ))}
      </ul>

      <motion.button
        onClick={() => {
          // Scroll to contact form
          const contactSection = document.getElementById("contact");
          if (contactSection) {
            contactSection.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }}
        className={`w-full py-4 rounded-2xl font-semibold text-white bg-gradient-to-r ${plan.color} hover:shadow-xl transition-all duration-300`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        {plan.period.includes("One-Time")
          ? "Order Now"
          : "Claim a Free Thumbnail"}
      </motion.button>
    </motion.div>
  );

  return (
    <section
      id='offers'
      className='py-16 md:py-20 lg:py-24 px-6 lg:px-12 bg-gradient-to-br from-gray-50 to-white'
      ref={ref}
    >
      <div className='max-w-7xl mx-auto'>
        {/* Section Header */}
        <motion.div
          className='text-center mb-20'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
        >
          <h2 className='text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6'>
            Choose Your <span className='text-gradient'>Perfect Plan</span>
          </h2>
          <p className='text-lg md:text-xl text-gray-600 max-w-3xl mx-auto'>
            From single thumbnails to complete branding packages - find the
            perfect solution for your content creation needs.
          </p>
        </motion.div>

        {/* Category Selection */}
        <motion.div
          className='mb-12'
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className='flex flex-wrap justify-center gap-4 mb-8'>
            {categories.map((category) => (
              <motion.button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 flex items-center gap-2 ${
                  selectedCategory === category.id
                    ? "bg-gradient-to-r from-[#FF4500] to-[#FF6B35] text-white shadow-lg"
                    : "bg-white/80 text-gray-700 hover:bg-white hover:shadow-md border border-gray-200"
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className={`${category.icon} text-sm`}></i>
                <span className='text-sm md:text-base'>{category.name}</span>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Selected Category Plans */}
        <motion.div
          key={selectedCategory}
          className='mb-20'
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className='grid md:grid-cols-3 gap-8 max-w-6xl mx-auto'>
            {getCurrentPlans().map((plan, index) => (
              <PricingCard
                key={plan.name}
                plan={plan}
                index={index}
                delay={0.1}
              />
            ))}
          </div>

          <motion.p
            className='text-center text-gray-600 mt-8'
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            Looking for a custom plan? Contact me to discuss your specific
            needs!
          </motion.p>
        </motion.div>
      </div>
    </section>
  );
}
